# LangGraph Configuration
NEXT_PUBLIC_API_URL=http://localhost:2024
NEXT_PUBLIC_ASSISTANT_ID=agent
# Do NOT prefix this with "NEXT_PUBLIC_" as we do not want this exposed in the client.
LANGSMITH_API_KEY=

# Production LangGraph Configuration (quickstart) - Uncomment to use
# NEXT_PUBLIC_ASSISTANT_ID="agent"
# This should be the deployment URL of your LangGraph server
# LANGGRAPH_API_URL="https://my-agent.default.us.langgraph.app"
# This should be the URL of your website + "/api". This is how you connect to the API proxy
# NEXT_PUBLIC_API_URL="https://my-website.com/api"
# LANGSMITH_API_KEY="lsv2_..."
