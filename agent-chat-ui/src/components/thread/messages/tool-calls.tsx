import { AIMessage, ToolMessage } from "@langchain/langgraph-sdk";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, ChevronUp, Co<PERSON>, Check } from "lucide-react";
import { MarkdownText } from "../markdown-text";

function isComplexValue(value: any): boolean {
  return Array.isArray(value) || (typeof value === "object" && value !== null);
}

// Component to render image with fallback to link
function ImageWithFallback({ url, index }: { url: string; index: number }) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (imageError) {
    return (
      <div className="p-3 border border-gray-200 rounded-lg bg-gray-50">
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 underline break-all text-sm"
        >
          {url}
        </a>
        <p className="text-xs text-gray-500 mt-1">Failed to load as image</p>
      </div>
    );
  }

  return (
    <div className="relative group">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-sm text-gray-500">Loading image...</div>
        </div>
      )}
      <img
        src={url}
        alt={`Image ${index + 1}`}
        className="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        style={{ maxHeight: '300px', objectFit: 'contain' }}
        onClick={() => window.open(url, '_blank')}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setImageError(true);
        }}
      />
    </div>
  );
}

// Component to render base64 image
function Base64Image({ data, mimeType, index }: { data: string; mimeType: string; index: number }) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const dataUrl = `data:${mimeType};base64,${data}`;

  console.log('Rendering Base64Image:', {
    index,
    mimeType,
    dataLength: data.length,
    dataPreview: data.substring(0, 50) + '...'
  }); // Debug log

  if (imageError) {
    return (
      <div className="p-3 border border-red-200 rounded-lg bg-red-50">
        <p className="text-sm text-red-600 font-medium">Base64 Image (failed to load)</p>
        <p className="text-xs text-red-500 mt-1">Type: {mimeType}</p>
        <p className="text-xs text-red-500">Size: {data.length} characters</p>
        <p className="text-xs text-red-500">Preview: {data.substring(0, 50)}...</p>
        <button
          className="mt-2 text-xs text-blue-600 underline"
          onClick={() => {
            console.log('Full base64 data:', data);
            console.log('Data URL:', dataUrl);
          }}
        >
          Debug in Console
        </button>
      </div>
    );
  }

  return (
    <div className="relative group">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg min-h-[100px]">
          <div className="text-sm text-gray-500">Loading base64 image...</div>
        </div>
      )}
      <img
        src={dataUrl}
        alt={`Base64 Image ${index + 1}`}
        className="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        style={{ maxHeight: '300px', objectFit: 'contain' }}
        onClick={() => {
          // Open base64 image in new tab
          const newWindow = window.open();
          if (newWindow) {
            newWindow.document.write(`<img src="${dataUrl}" style="max-width: 100%; height: auto;" />`);
          }
        }}
        onLoad={() => {
          console.log('Base64 image loaded successfully');
          setIsLoading(false);
        }}
        onError={(e) => {
          console.error('Base64 image failed to load:', e);
          console.log('Failed data URL:', dataUrl);
          setIsLoading(false);
          setImageError(true);
        }}
      />
      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
        Base64 Image ({mimeType})
      </div>
    </div>
  );
}

// Enhanced JSON renderer with syntax highlighting and copy functionality
function EnhancedJsonRenderer({ data, isExpanded, onToggleExpand }: {
  data: any;
  isExpanded: boolean;
  onToggleExpand: () => void;
}) {
  const [copied, setCopied] = useState(false);

  const jsonString = JSON.stringify(data, null, 2);
  const lines = jsonString.split('\n');
  const shouldTruncate = lines.length > 10;
  const displayLines = shouldTruncate && !isExpanded ? lines.slice(0, 10) : lines;

  // Extract base64 images from JSON data
  const extractBase64FromJson = (obj: any): Array<{data: string, mimeType: string, path: string}> => {
    const images: Array<{data: string, mimeType: string, path: string}> = [];

    const traverse = (current: any, path: string = '') => {
      if (typeof current === 'object' && current !== null) {
        for (const [key, value] of Object.entries(current)) {
          const currentPath = path ? `${path}.${key}` : key;

          if (typeof value === 'string') {
            // First check if this string contains JSON with base64Data
            try {
              const parsed = JSON.parse(value);
              const nestedImages = extractBase64FromJson(parsed);
              images.push(...nestedImages.map(img => ({...img, path: `${currentPath}.${img.path}`})));
            } catch {
              // Not JSON, check if it's a base64 string directly
            }

            if (key.toLowerCase().includes('base64') ||
               key.toLowerCase().includes('image') ||
               value.startsWith('data:image/') ||
               (value.length > 100 && /^[A-Za-z0-9+\/]+=*$/.test(value))) {

            let base64Data = value;
            let mimeType = 'image/png';

            // Handle data URLs
            if (value.startsWith('data:image/')) {
              const match = value.match(/data:image\/([^;]+);base64,(.+)/);
              if (match) {
                mimeType = `image/${match[1]}`;
                base64Data = match[2];
              }
            } else {
              // Detect format from base64 header
              if (base64Data.startsWith('/9j/')) mimeType = 'image/jpeg';
              else if (base64Data.startsWith('iVBORw0KGgo')) mimeType = 'image/png';
              else if (base64Data.startsWith('R0lGOD')) mimeType = 'image/gif';
              else if (base64Data.startsWith('UklGR')) mimeType = 'image/webp';
            }

            if (base64Data.length > 50) {
              images.push({ data: base64Data, mimeType, path: currentPath });
            }
            }
          }

          if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
            traverse(value, currentPath);
          }
        }
      } else if (Array.isArray(current)) {
        current.forEach((item, index) => {
          traverse(item, `${path}[${index}]`);
        });
      }
    };

    traverse(obj);
    return images;
  };

  const base64Images = extractBase64FromJson(data);
  console.log('Found base64 images in JSON:', base64Images);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonString).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const renderJsonLine = (line: string, index: number) => {
    // Simple syntax highlighting
    const highlightedLine = line
      .replace(/(".*?"):/g, '<span class="text-blue-600 font-medium">$1</span>:')
      .replace(/:\s*(".*?")/g, ': <span class="text-green-600">$1</span>')
      .replace(/:\s*(true|false)/g, ': <span class="text-purple-600">$1</span>')
      .replace(/:\s*(null)/g, ': <span class="text-gray-500">$1</span>')
      .replace(/:\s*(\d+\.?\d*)/g, ': <span class="text-orange-600">$1</span>');

    return (
      <div
        key={index}
        className="font-mono text-sm leading-relaxed"
        dangerouslySetInnerHTML={{ __html: highlightedLine }}
      />
    );
  };

  return (
    <div className="relative space-y-4">
      {/* Render extracted images first */}
      {base64Images.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-500 font-medium">
              Images found in JSON ({base64Images.length})
            </span>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {base64Images.map((img, index) => (
              <div key={index} className="relative">
                <Base64Image
                  data={img.data}
                  mimeType={img.mimeType}
                  index={index}
                />
                <div className="text-xs text-gray-500 mt-1">
                  Path: {img.path}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* JSON Data */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-gray-500 font-medium">JSON Data</span>
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-1 px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
          >
            {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
            {copied ? 'Copied!' : 'Copy'}
          </button>
        </div>

        <div className="bg-gray-50 rounded-lg p-3 border">
          {displayLines.map((line, index) => {
            // Hide base64 data in JSON display if we're showing images above
            if (base64Images.length > 0) {
              const hiddenLine = line.replace(
                /"[^"]*base64[^"]*":\s*"[A-Za-z0-9+\/=]{50,}"/gi,
                '"$1": "[Base64 Image Data - Rendered Above]"'
              );
              return renderJsonLine(hiddenLine, index);
            }
            return renderJsonLine(line, index);
          })}
          {shouldTruncate && !isExpanded && (
            <div className="text-gray-500 text-sm mt-2">
              ... {lines.length - 10} more lines
            </div>
          )}
        </div>

        {shouldTruncate && (
          <button
            onClick={onToggleExpand}
            className="mt-2 flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            {isExpanded ? 'Show Less' : 'Show More'}
          </button>
        )}
      </div>
    </div>
  );
}

// Enhanced content renderer that handles images and links
function EnhancedContentRenderer({ content }: { content: string }) {
  const [showFullContent, setShowFullContent] = useState(false);
  // General URL pattern
  const urlPattern = /https?:\/\/[^\s]+/gi;
  const allUrls = content.match(urlPattern) || [];

  // Function to check if a URL is likely an image
  const isLikelyImageUrl = (url: string): boolean => {
    // Check for common image file extensions
    if (/\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?[^\s]*)?$/i.test(url)) {
      return true;
    }

    // Check for common image hosting domains and patterns
    const imageHostPatterns = [
      /mdn\.alipayobjects\.com.*\/img\//i,
      /.*\.amazonaws\.com.*\.(jpg|jpeg|png|gif|webp|svg)/i,
      /.*\.cloudfront\.net.*\.(jpg|jpeg|png|gif|webp|svg)/i,
      /.*\.googleapis\.com.*image/i,
      /.*\.imgur\.com/i,
      /.*\.github\.com.*\.(jpg|jpeg|png|gif|webp|svg)/i,
      /.*\/images?\//i,
      /.*\/img\//i,
      /.*\/photos?\//i,
      /.*\/pictures?\//i,
      /.*\/assets.*\.(jpg|jpeg|png|gif|webp|svg)/i,
    ];

    return imageHostPatterns.some(pattern => pattern.test(url));
  };

  // Function to extract base64 image data
  const extractBase64Images = (text: string): Array<{data: string, mimeType: string}> => {
    const base64Images: Array<{data: string, mimeType: string}> = [];

    console.log('Extracting base64 from text:', text.substring(0, 500) + '...');

    // More flexible patterns to match base64Data, including escaped JSON
    const patterns = [
      /"base64Data":\s*"([^"]+)"/gi,
      /"base64":\s*"([^"]+)"/gi,
      /"data":\s*"data:image\/[^;]+;base64,([^"]+)"/gi,
      /"image":\s*"data:image\/[^;]+;base64,([^"]+)"/gi,
      /data:image\/[^;]+;base64,([A-Za-z0-9+\/=]+)/gi,
      // Handle escaped JSON strings
      /\\"base64Data\\":\s*\\"([^"]+)\\"/gi,
      /\\"base64\\":\s*\\"([^"]+)\\"/gi,
    ];

    patterns.forEach((pattern, index) => {
      console.log(`Testing pattern ${index}:`, pattern);
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const base64Data = match[1];
        console.log(`Found match with pattern ${index}:`, {
          matchLength: base64Data?.length,
          preview: base64Data?.substring(0, 50) + '...'
        });

        if (base64Data && base64Data.length > 20) { // Lower threshold for small images
          // Try to determine mime type from base64 header or assume common types
          let mimeType = 'image/png'; // default

          if (base64Data.startsWith('/9j/')) mimeType = 'image/jpeg';
          else if (base64Data.startsWith('iVBORw0KGgo')) mimeType = 'image/png';
          else if (base64Data.startsWith('R0lGOD')) mimeType = 'image/gif';
          else if (base64Data.startsWith('UklGR')) mimeType = 'image/webp';
          else if (base64Data.startsWith('Qk0')) mimeType = 'image/bmp';

          console.log('Adding base64 image:', { mimeType, dataLength: base64Data.length });
          base64Images.push({ data: base64Data, mimeType });
        }
      }
    });

    // Also try to find any long base64-like strings that might be images
    const genericBase64Pattern = /[A-Za-z0-9+\/]{50,}={0,2}/g;
    let match;
    while ((match = genericBase64Pattern.exec(text)) !== null) {
      const base64Data = match[0];
      if (base64Data.length > 100) { // Lower threshold for detection
        let mimeType = 'image/png'; // default

        if (base64Data.startsWith('/9j/')) mimeType = 'image/jpeg';
        else if (base64Data.startsWith('iVBORw0KGgo')) mimeType = 'image/png';
        else if (base64Data.startsWith('R0lGOD')) mimeType = 'image/gif';
        else if (base64Data.startsWith('UklGR')) mimeType = 'image/webp';

        // Check if we haven't already added this data
        if (!base64Images.some(img => img.data === base64Data)) {
          base64Images.push({ data: base64Data, mimeType });
        }
      }
    }

    console.log('Found base64 images:', base64Images.length); // Debug log
    return base64Images;
  };

  const imageUrls = allUrls.filter(isLikelyImageUrl);
  const base64Images = extractBase64Images(content);

  console.log('EnhancedContentRenderer:', {
    contentLength: content.length,
    imageUrls: imageUrls.length,
    base64Images: base64Images.length,
    contentPreview: content.substring(0, 200) + '...'
  }); // Debug log

  // If content contains images (URLs or base64), render them separately
  if (imageUrls.length > 0 || base64Images.length > 0) {
    return (
      <div className="space-y-3">
        {/* Render images */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {/* Render URL images */}
          {imageUrls.map((url, index) => (
            <ImageWithFallback key={`url-${index}`} url={url} index={index} />
          ))}
          {/* Render base64 images */}
          {base64Images.map((img, index) => (
            <Base64Image
              key={`base64-${index}`}
              data={img.data}
              mimeType={img.mimeType}
              index={index}
            />
          ))}
        </div>

        {/* Render text content with clickable links, but hide base64 data */}
        <div className="text-sm">
          <MarkdownText>
            {content
              .replace(/"base64Data":\s*"[^"]+"/gi, '"base64Data": "[Base64 Image Data - Rendered Above]"')
              .replace(urlPattern, (url) => `[${url}](${url})`)}
          </MarkdownText>
        </div>
      </div>
    );
  }

  // If no images but has URLs, make them clickable
  if (allUrls.length > 0) {
    return (
      <div className="text-sm">
        <MarkdownText>
          {content.replace(urlPattern, (url) => `[${url}](${url})`)}
        </MarkdownText>
      </div>
    );
  }

  // Default rendering for content without URLs
  return (
    <div className="text-sm">
      <MarkdownText>{content}</MarkdownText>
    </div>
  );
}

export function ToolCalls({
  toolCalls,
}: {
  toolCalls: AIMessage["tool_calls"];
}) {
  if (!toolCalls || toolCalls.length === 0) return null;

  return (
    <div className="mx-auto grid max-w-3xl grid-rows-[1fr_auto] gap-2">
      {toolCalls.map((tc, idx) => {
        const args = tc.args as Record<string, any>;
        const hasArgs = Object.keys(args).length > 0;
        return (
          <div
            key={idx}
            className="overflow-hidden rounded-lg border border-gray-200"
          >
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-2">
              <h3 className="font-medium text-gray-900">
                {tc.name}
                {tc.id && (
                  <code className="ml-2 rounded bg-gray-100 px-2 py-1 text-sm">
                    {tc.id}
                  </code>
                )}
              </h3>
            </div>
            {hasArgs ? (
              <table className="min-w-full divide-y divide-gray-200">
                <tbody className="divide-y divide-gray-200">
                  {Object.entries(args).map(([key, value], argIdx) => (
                    <tr key={argIdx}>
                      <td className="px-4 py-2 text-sm font-medium whitespace-nowrap text-gray-900">
                        {key}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500">
                        {isComplexValue(value) ? (
                          <code className="rounded bg-gray-50 px-2 py-1 font-mono text-sm break-all">
                            {JSON.stringify(value, null, 2)}
                          </code>
                        ) : (
                          String(value)
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <code className="block p-3 text-sm">{"{}"}</code>
            )}
          </div>
        );
      })}
    </div>
  );
}

export function ToolResult({ message }: { message: ToolMessage }) {
  const [isExpanded, setIsExpanded] = useState(false);

  let parsedContent: any;
  let isJsonContent = false;

  try {
    if (typeof message.content === "string") {
      parsedContent = JSON.parse(message.content);
      isJsonContent = isComplexValue(parsedContent);
    }
  } catch {
    // Content is not JSON, use as is
    parsedContent = message.content;
  }

  const contentStr = isJsonContent
    ? JSON.stringify(parsedContent, null, 2)
    : String(message.content);
  const contentLines = contentStr.split("\n");
  const shouldTruncate = contentLines.length > 4 || contentStr.length > 500;
  const displayedContent =
    shouldTruncate && !isExpanded
      ? contentStr.length > 500
        ? contentStr.slice(0, 500) + "..."
        : contentLines.slice(0, 4).join("\n") + "\n..."
      : contentStr;

  return (
    <div className="mx-auto grid max-w-3xl grid-rows-[1fr_auto] gap-2">
      <div className="overflow-hidden rounded-lg border border-gray-200">
        <div className="border-b border-gray-200 bg-gray-50 px-4 py-2">
          <div className="flex flex-wrap items-center justify-between gap-2">
            {message.name ? (
              <h3 className="font-medium text-gray-900">
                Tool Result:{" "}
                <code className="rounded bg-gray-100 px-2 py-1">
                  {message.name}
                </code>
              </h3>
            ) : (
              <h3 className="font-medium text-gray-900">Tool Result</h3>
            )}
            {message.tool_call_id && (
              <code className="ml-2 rounded bg-gray-100 px-2 py-1 text-sm">
                {message.tool_call_id}
              </code>
            )}
          </div>
        </div>
        <motion.div
          className="min-w-full bg-gray-100"
          initial={false}
          animate={{ height: "auto" }}
          transition={{ duration: 0.3 }}
        >
          <div className="p-3">
            <AnimatePresence
              mode="wait"
              initial={false}
            >
              <motion.div
                key={isExpanded ? "expanded" : "collapsed"}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {isJsonContent ? (
                  <EnhancedJsonRenderer
                    data={parsedContent}
                    isExpanded={isExpanded}
                    onToggleExpand={() => setIsExpanded(!isExpanded)}
                  />
                ) : (
                  <div className="block">
                    <EnhancedContentRenderer content={contentStr} />
                  </div>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
          {shouldTruncate && !isJsonContent && (
            <motion.button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex w-full cursor-pointer items-center justify-center border-t-[1px] border-gray-200 py-2 text-gray-500 transition-all duration-200 ease-in-out hover:bg-gray-50 hover:text-gray-600"
              initial={{ scale: 1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isExpanded ? <ChevronUp /> : <ChevronDown />}
            </motion.button>
          )}
        </motion.div>
      </div>
    </div>
  );
}
