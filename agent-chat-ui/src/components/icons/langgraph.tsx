export function LangGraphLogoSVG({
  className,
  width,
  height,
}: {
  width?: number;
  height?: number;
  className?: string;
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 120 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* 背景圆角矩形 */}
      <rect
        x="4"
        y="4"
        width="112"
        height="52"
        rx="26"
        fill="#264849"
        stroke="#1a3334"
        strokeWidth="2"
      />

      {/* 装饰性图形元素 */}
      <circle cx="20" cy="30" r="3" fill="#4a9eff" opacity="0.8" />
      <circle cx="100" cy="30" r="3" fill="#4a9eff" opacity="0.8" />

      {/* "但" 字 */}
      <text
        x="35"
        y="38"
        fontFamily="serif"
        fontSize="24"
        fontWeight="bold"
        fill="white"
        textAnchor="middle"
      >
        但
      </text>

      {/* "问" 字 */}
      <text
        x="85"
        y="38"
        fontFamily="serif"
        fontSize="24"
        fontWeight="bold"
        fill="white"
        textAnchor="middle"
      >
        问
      </text>

      {/* 中间分隔线 */}
      <line
        x1="60"
        y1="18"
        x2="60"
        y2="42"
        stroke="#4a9eff"
        strokeWidth="2"
        opacity="0.6"
      />
    </svg>
  );
}
