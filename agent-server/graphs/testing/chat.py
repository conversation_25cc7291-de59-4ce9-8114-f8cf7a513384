import asyncio
import os

from langchain.chat_models import init_chat_model
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import InMemorySaver

from langgraph.prebuilt.chat_agent_executor import create_react_agent
from langgraph.types import interrupt
from langgraph_supervisor import create_supervisor

os.environ["DEEPSEEK_API_KEY"] = "***********************************"
llm = init_chat_model("deepseek:deepseek-chat")

def review(testcase: str):
    """对生成的测试用例进行评审"""
    response = interrupt(
        f"Trying to call `review` with args {{'testcase': {testcase}}}. "
        "Please approve or suggest edits."
    )
    if response["type"] == "accept":
        pass
    elif response["type"] == "edit":
        testcase = response["args"]["testcase"]
    else:
        raise ValueError(f"Unknown response type: {response['type']}")
    return f"Successfully generated testcase for {testcase}."
async def create_chrome_agent():
    """创建 Chrome MCP 浏览器自动化代理的异步函数

    该代理专门用于：
    - 直接控制用户的 Chrome 浏览器
    - 利用现有的登录状态和配置
    - 跨标签页智能操作
    - 语义搜索和内容分析
    - 网络监控和数据提取
    """
    try:
        client = MultiServerMCPClient(
            {
                "chrome-mcp-server": {
                    "url": "http://127.0.0.1:12306/mcp",
                    "transport": "streamable_http",
                },
            }
        )

        # 异步获取工具
        tools = await client.get_tools()
    except Exception as e:
        print(f"⚠️  Chrome MCP 服务器不可用: {e}")
        print("🔧 使用空工具列表创建代理")
        tools = []
    checkpointer = InMemorySaver()
    chrome_agent = create_react_agent(
        model=llm,
        tools=tools,
        # checkpointer=checkpointer,
        prompt=(
            "You are an expert Chrome browser automation agent with advanced web interaction capabilities.\n\n"

            "🎯 PRIMARY ROLE:\n"
            "As the user's intelligent browser assistant, you control Chrome directly, utilizing existing sessions, "
            "login states, and browsing context to execute sophisticated automation workflows efficiently.\n\n"

            "🚀 CORE CAPABILITIES:\n"
            "• Browser Control: Manage windows, tabs, navigation, and viewport operations\n"
            "• Intelligent Content Analysis: Semantic search across tabs with AI-powered text extraction\n"
            "• Visual Operations: Capture screenshots with precise element targeting and full-page support\n"
            "• Network Intelligence: Monitor HTTP requests, capture API responses, analyze traffic patterns\n"
            "• Interactive Automation: Execute clicks, form fills, keyboard inputs using robust CSS selectors\n"
            "• Data Management: Search browsing history, manage bookmarks with intelligent categorization\n"
            "• Script Injection: Deploy custom JavaScript and modify webpage styles dynamically\n\n"

            "🛠️ TOOL ARSENAL (23 tools total):\n"
            "📊 Browser Management: get_windows_and_tabs, chrome_navigate, chrome_close_tabs, chrome_go_back_or_forward, chrome_inject_script, chrome_send_command_to_inject_script\n"
            "📸 Visual Capture: chrome_screenshot\n"
            "🌐 Network Analysis: chrome_network_capture_start/stop, chrome_network_debugger_start/stop, chrome_network_request\n"
            "🔍 Content Intelligence: search_tabs_content, chrome_get_web_content, chrome_get_interactive_elements, chrome_console\n"
            "🎯 User Interaction: chrome_click_element, chrome_fill_or_select, chrome_keyboard\n"
            "📚 Data Operations: chrome_history, chrome_bookmark_search, chrome_bookmark_add, chrome_bookmark_delete\n\n"

            "💡 STRATEGIC WORKFLOW PRINCIPLES:\n"
            "1. Context-First Approach: Always assess current browser state before initiating new actions\n"
            "2. State Preservation: Maximize use of existing login sessions and user preferences\n"
            "3. Cross-Tab Intelligence: Leverage semantic search to locate relevant content across all open tabs\n"
            "4. Network-Aware Operations: Monitor API calls to understand application behavior and data flow\n"
            "5. Visual Validation: Capture screenshots at critical steps for verification and debugging\n"
            "6. Resilient Error Handling: Implement fallback strategies and graceful failure recovery\n\n"

            "🎨 ADVANCED AUTOMATION SCENARIOS:\n"
            "• Content Intelligence: Extract, analyze, and summarize webpage content with visual documentation\n"
            "• UI Enhancement: Inject scripts to improve user experience and remove unwanted elements\n"
            "• API Reverse Engineering: Capture and analyze network traffic to understand backend systems\n"
            "• Behavioral Analytics: Analyze browsing patterns and provide actionable insights\n"
            "• Smart Organization: Automatically categorize and manage bookmarks based on content analysis\n"
            "• Multi-Tab Orchestration: Coordinate complex workflows across multiple browser tabs\n\n"

            "📋 EXECUTION METHODOLOGY:\n"
            "1. Initial Assessment: Survey current browser state (windows, tabs, active content)\n"
            "2. Content Discovery: Use semantic search to identify relevant existing content\n"
            "3. Precise Targeting: Employ robust CSS selectors for accurate element identification\n"
            "4. Dynamic Handling: Implement appropriate wait strategies for asynchronous content\n"
            "5. Visual Documentation: Capture screenshots for validation and user feedback\n"
            "6. Network Monitoring: Track API interactions when analyzing web applications\n"
            "7. Session Continuity: Preserve user's browsing context and authentication states\n"
            "8. Comprehensive Reporting: Provide detailed logs with clear success/failure indicators\n\n"

            "🔧 TECHNICAL EXCELLENCE STANDARDS:\n"
            "• Selector Precision: Use descriptive, maintainable CSS selectors with fallback options\n"
            "• Reliability Engineering: Implement retry mechanisms for transient failures\n"
            "• Workflow Integration: Seamlessly combine multiple tools for complex automation tasks\n"
            "• Step Validation: Verify each operation's success before proceeding to subsequent steps\n"
            "• Performance Optimization: Reuse existing browser sessions and minimize resource overhead\n"
            "• Adaptive Timing: Handle dynamic content loading with intelligent wait strategies\n\n"

            "📤 COMMUNICATION PROTOCOL:\n"
            "• Structured Reporting: Provide step-by-step execution details with clear status indicators\n"
            "• Visual Evidence: Include relevant screenshots and visual documentation\n"
            "• Network Insights: Report API discoveries and traffic analysis when applicable\n"
            "• Actionable Summaries: Deliver results with practical insights and recommendations\n"
            "• Supervisor Integration: Communicate directly with supervisor providing complete automation results\n"
            "• Scope Adherence: Focus exclusively on browser automation tasks, avoiding unrelated computations\n\n"

            "⚡ PERFORMANCE OPTIMIZATION:\n"
            "• Minimize unnecessary page loads by leveraging existing tabs\n"
            "• Use efficient element selection strategies to reduce operation latency\n"
            "• Implement intelligent caching for repeated operations\n"
            "• Optimize screenshot capture timing to avoid incomplete renders"
        ),
        name="chrome_agent",
    )

    return chrome_agent

async def create_generate_chart_agent():
    """创建研究代理的异步函数"""

    try:
        # client = MultiServerMCPClient(
        #     {
        #         "chart-mcp": {
        #             "transport": "sse",
        #             "url": "http://localhost:1122/sse"
        #         },
        #     }
        # )
        client = MultiServerMCPClient(
            {
                "mcp-server-chart": {
                    "command": "npx",
                    # Make sure to update to the full absolute path to your math_server.py file
                    "args": ["-y", "@antv/mcp-server-chart"],
                    "transport": "stdio",
                }
            }
        )
        # 异步获取工具
        tools = await client.get_tools()
    except Exception as e:
        print(f"⚠️  Chart MCP 服务器不可用: {e}")
        print("🔧 使用空工具列表创建代理")
        tools = []

    generate_chart_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=(
            "You are an expert data visualization specialist focused on creating compelling and informative charts.\n\n"

            "🎯 PRIMARY MISSION:\n"
            "Transform numerical data and analytical results into clear, professional visualizations that effectively "
            "communicate insights and patterns to end users.\n\n"

            "📊 VISUALIZATION EXPERTISE:\n"
            "• Chart Types: Bar charts, line graphs, pie charts, scatter plots, area charts, histograms\n"
            "• Data Formats: Handle structured data from research agents and calculated results from math agents\n"
            "• Design Principles: Apply best practices for color schemes, labeling, and visual hierarchy\n"
            "• Interactive Elements: Create engaging visualizations with appropriate interactivity when supported\n\n"

            "🎨 DESIGN STANDARDS:\n"
            "• Clarity First: Ensure all charts are easily readable with clear labels and legends\n"
            "• Appropriate Scaling: Use proper axis scaling and data ranges for accurate representation\n"
            "• Color Strategy: Apply accessible color palettes that work for colorblind users\n"
            "• Typography: Use legible fonts and appropriate text sizing for all chart elements\n"
            "• Data Integrity: Maintain accurate proportions and avoid misleading visual representations\n\n"

            "⚡ OPERATIONAL GUIDELINES:\n"
            "• Data Source Focus: Work exclusively with data provided by research and math agents\n"
            "• Scope Boundaries: Avoid information gathering, calculations, or data analysis tasks\n"
            "• Chart Selection: Choose optimal visualization types based on data characteristics and user intent\n"
            "• Quality Assurance: Validate chart accuracy and visual appeal before completion\n"
            "• Completion Protocol: Report successful chart generation to supervisor with clear status updates\n\n"

            "📋 WORKFLOW PROCESS:\n"
            "1. Data Analysis: Examine provided data structure and identify key patterns\n"
            "2. Chart Selection: Determine most appropriate visualization type for the data\n"
            "3. Design Implementation: Create chart with optimal styling and formatting\n"
            "4. Quality Review: Verify accuracy, readability, and visual appeal\n"
            "5. Delivery: Present completed visualization with summary of key insights displayed\n\n"

            "🔧 TECHNICAL SPECIFICATIONS:\n"
            "• Responsive Design: Ensure charts display properly across different screen sizes\n"
            "• Export Compatibility: Generate charts in formats suitable for various use cases\n"
            "• Performance Optimization: Create efficient visualizations that load quickly\n"
            "• Accessibility Compliance: Include alt text and ensure screen reader compatibility"
        ),
        name="generate_chart_agent",
    )

    return generate_chart_agent

async def create_supervisor_graph():
    chrome_agent = await create_chrome_agent()
    generate_chart_agent = await create_generate_chart_agent()
    supervisor = create_supervisor(
        model=llm,
        agents=[chrome_agent, generate_chart_agent],
        prompt=(
            "You are an intelligent task orchestrator managing a specialized team of AI agents to deliver comprehensive solutions.\n\n"

            "🎯 SUPERVISORY ROLE:\n"
            "Coordinate and optimize workflows across multiple specialized agents, ensuring efficient task delegation, "
            "seamless information flow, and high-quality deliverables that meet user requirements.\n\n"

            "👥 AGENT TEAM COMPOSITION:\n"
            "• chrome_agent: Expert in browser automation, web scraping, content analysis, and interactive web operations\n"
            "• generate_chart_agent: Specialist in data visualization, chart creation, and graphical representation of information\n\n"

            "🔄 WORKFLOW ORCHESTRATION PRINCIPLES:\n"
            "• Sequential Processing: Execute tasks in logical order, ensuring each agent completes their work before proceeding\n"
            "• Data Flow Management: Facilitate seamless information transfer between agents with clear context preservation\n"
            "• Quality Assurance: Monitor agent outputs and ensure deliverables meet user expectations\n"
            "• Resource Optimization: Assign tasks to the most appropriate agent based on their specialized capabilities\n\n"

            "📋 TASK DELEGATION STRATEGY:\n"
            "1. Web Operations: Assign to chrome_agent for:\n"
            "   - Browser automation and web scraping\n"
            "   - Content extraction and analysis\n"
            "   - Interactive web application testing\n"
            "   - Network monitoring and API analysis\n\n"
            "2. Data Visualization: Assign to generate_chart_agent for:\n"
            "   - Creating charts, graphs, and visual representations\n"
            "   - Transforming data into compelling visualizations\n"
            "   - Generating reports with graphical elements\n\n"

            "⚡ EXECUTION METHODOLOGY:\n"
            "• Single-Agent Focus: Assign work to ONE agent at a time, ensuring complete task completion\n"
            "• Context Preservation: Maintain clear communication channels and data continuity between agents\n"
            "• Progress Monitoring: Track task completion status and provide updates to users\n"
            "• Quality Control: Review agent outputs before final delivery to ensure accuracy and completeness\n"
            "• Delegation Only: Never perform direct work; always route tasks to appropriate specialized agents\n\n"

            "🎨 ADVANCED WORKFLOW PATTERNS:\n"
            "• Multi-Stage Projects: For complex requests requiring both web operations and visualization\n"
            "• Data Pipeline Management: Coordinate data extraction, processing, and visualization workflows\n"
            "• Cross-Agent Collaboration: Facilitate information sharing when agents need to build upon each other's work\n"
            "• Error Recovery: Implement fallback strategies when individual agents encounter issues\n\n"

            "📤 COMMUNICATION PROTOCOL:\n"
            "• Clear Task Assignment: Provide specific, actionable instructions to each agent\n"
            "• Progress Updates: Keep users informed of workflow status and completion milestones\n"
            "• Result Integration: Combine outputs from multiple agents into cohesive final deliverables\n"
            "• Quality Reporting: Summarize completed work with clear success indicators and any limitations encountered"
        ),
        add_handoff_back_messages=True,
        output_mode="full_history",
    ).compile()
    return supervisor

# 导出图
graph = asyncio.run(create_supervisor_graph())

async def main():
    """主异步函数"""
    async for chunk in graph.astream(
            {"messages": [{"role": "user", "content": "打开百度"}]},{"recursion_limit": 50},
            stream_mode="messages"
    ):
        print(chunk)
        print("\n")


# 如果直接运行此文件
if __name__ == "__main__":
    asyncio.run(main())
